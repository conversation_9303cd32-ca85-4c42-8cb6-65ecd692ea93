<!-- 步驟導航條 -->
<div class="step-navigator" [ngClass]="customClass">
  <div class="step-nav">
    <div 
      *ngFor="let step of processedSteps; let i = index"
      class="step-wrapper"
      [class.clickable]="isStepClickable(step)"
      (click)="onStepClick(step.stepNumber, step)">
      
      <!-- 步驟項目 -->
      <div [ngClass]="getStepClasses(step)">
        <!-- 步驟圖標（如果有） -->
        <nb-icon 
          *ngIf="step.icon" 
          [icon]="step.icon" 
          class="step-icon">
        </nb-icon>
        
        <!-- 步驟內容 -->
        <div class="step-content">
          <!-- 步驟編號和標籤 -->
          <span class="step-text">
            <span *ngIf="showStepNumber" class="step-number">{{ step.stepNumber }}.</span>
            <span class="step-label">{{ step.label }}</span>
          </span>
        </div>
        
        <!-- 完成狀態圖標 -->
        <nb-icon 
          *ngIf="step.status === StepStatus.COMPLETED" 
          icon="checkmark-outline" 
          class="step-check-icon">
        </nb-icon>
      </div>
      
      <!-- 步驟連接線（除了最後一個步驟） -->
      <div 
        *ngIf="i < processedSteps.length - 1" 
        class="step-connector"
        [ngClass]="{
          'completed': step.status === StepStatus.COMPLETED,
          'active': step.status === StepStatus.ACTIVE
        }">
      </div>
    </div>
  </div>
</div>
