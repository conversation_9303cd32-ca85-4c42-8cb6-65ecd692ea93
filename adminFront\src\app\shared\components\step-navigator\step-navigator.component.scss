@import '../../../@theme/styles/colors';

.step-navigator {
  width: 100%;

  .step-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid $border-light;
    padding-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .step-wrapper {
      display: flex;
      align-items: center;
      position: relative;

      &.clickable {
        cursor: pointer;

        .step-item {
          &:hover {
            transform: translateY(-1px);
            box-shadow: $shadow-md;
          }
        }
      }

      .step-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        font-weight: 500;
        transition: $transition-normal;
        position: relative;
        min-width: 120px;
        justify-content: center;
        gap: 0.5rem;

        .step-icon {
          font-size: 1rem;
          flex-shrink: 0;
        }

        .step-content {
          display: flex;
          align-items: center;
          flex: 1;

          .step-text {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            .step-number {
              font-weight: 600;
            }

            .step-label {
              white-space: nowrap;
            }
          }
        }

        .step-check-icon {
          font-size: 1rem;
          margin-left: 0.25rem;
          flex-shrink: 0;
        }

        // 狀態樣式
        &.active {
          background: $gradient-primary;
          color: $text-light;
          box-shadow: $shadow-sm;

          .step-icon,
          .step-check-icon {
            color: $text-light;
          }
        }

        &.completed {
          background-color: $success-base;
          color: $text-light;
          box-shadow: $shadow-sm;

          .step-icon,
          .step-check-icon {
            color: $text-light;
          }
        }

        &.pending {
          background-color: $bg-secondary;
          color: $text-muted;
          border: 1px solid $border-light;

          .step-icon {
            color: $text-muted;
          }
        }
      }

      .step-connector {
        width: 2rem;
        height: 2px;
        background-color: $border-light;
        margin: 0 0.5rem;
        transition: $transition-normal;

        &.completed {
          background-color: $success-base;
        }

        &.active {
          background: $gradient-primary;
        }
      }
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .step-navigator {
    .step-nav {
      .step-wrapper {
        .step-item {
          font-size: 0.875rem;
          padding: 0.4rem 0.8rem;
          min-width: 100px;

          .step-content {
            .step-text {
              .step-label {
                font-size: 0.8rem;
              }
            }
          }
        }

        .step-connector {
          width: 1.5rem;
          margin: 0 0.25rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .step-navigator {
    .step-nav {
      flex-direction: column;
      gap: 1rem;

      .step-wrapper {
        width: 100%;
        justify-content: center;

        .step-connector {
          display: none;
        }

        .step-item {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
}

// 深色主題支持
:host-context(.dark-theme) {
  .step-navigator {
    .step-nav {
      border-bottom-color: $dark-border;

      .step-wrapper {
        .step-item {
          &.pending {
            background-color: $dark-bg-secondary;
            color: $dark-text-secondary;
            border-color: $dark-border;
          }
        }

        .step-connector {
          background-color: $dark-border;
        }
      }
    }
  }
}
