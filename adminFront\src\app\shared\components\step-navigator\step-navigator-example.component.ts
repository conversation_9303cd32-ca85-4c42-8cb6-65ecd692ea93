import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbCardModule, NbButtonModule } from '@nebular/theme';
import { StepNavigatorComponent } from './step-navigator.component';
import { StepConfig } from '../../interfaces/step-config.interface';

/**
 * 步驟導航條使用示例組件
 * 展示如何在其他組件中使用 StepNavigatorComponent
 */
@Component({
  selector: 'app-step-navigator-example',
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbButtonModule,
    StepNavigatorComponent
  ],
  template: `
    <nb-card>
      <nb-card-header>
        <h5>步驟導航條使用示例</h5>
      </nb-card-header>
      
      <nb-card-body>
        <!-- 基本使用 -->
        <div class="example-section">
          <h6>基本使用（不可點擊）</h6>
          <app-step-navigator
            [steps]="basicSteps"
            [currentStep]="currentStep1"
            [allowClickNavigation]="false">
          </app-step-navigator>
        </div>

        <!-- 可點擊導航 -->
        <div class="example-section">
          <h6>可點擊導航</h6>
          <app-step-navigator
            [steps]="clickableSteps"
            [currentStep]="currentStep2"
            [allowClickNavigation]="true"
            (stepClick)="onStepClick($event)">
          </app-step-navigator>
        </div>

        <!-- 自定義樣式 -->
        <div class="example-section">
          <h6>自定義樣式</h6>
          <app-step-navigator
            [steps]="customSteps"
            [currentStep]="currentStep3"
            [showStepNumber]="false"
            customClass="custom-step-nav">
          </app-step-navigator>
        </div>

        <!-- 多步驟流程 -->
        <div class="example-section">
          <h6>多步驟流程</h6>
          <app-step-navigator
            [steps]="multiSteps"
            [currentStep]="currentStep4"
            [allowClickNavigation]="true"
            (stepClick)="onMultiStepClick($event)">
          </app-step-navigator>
        </div>
      </nb-card-body>

      <nb-card-footer>
        <div class="button-group">
          <button nbButton status="primary" (click)="nextStep()">下一步</button>
          <button nbButton status="basic" (click)="prevStep()">上一步</button>
          <button nbButton status="info" (click)="reset()">重置</button>
        </div>
      </nb-card-footer>
    </nb-card>
  `,
  styles: [`
    .example-section {
      margin-bottom: 2rem;
      padding: 1rem;
      border: 1px solid #e4e9f2;
      border-radius: 0.25rem;
      
      h6 {
        margin-bottom: 1rem;
        color: #2c3e50;
      }
    }

    .button-group {
      display: flex;
      gap: 0.5rem;
    }

    :host ::ng-deep .custom-step-nav {
      .step-nav {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
      }
    }
  `]
})
export class StepNavigatorExampleComponent {
  // 基本步驟
  basicSteps: StepConfig[] = [
    { id: 1, label: '開始', icon: 'play-outline' },
    { id: 2, label: '處理中', icon: 'loader-outline' },
    { id: 3, label: '完成', icon: 'checkmark-outline' }
  ];

  // 可點擊步驟
  clickableSteps: StepConfig[] = [
    { id: 1, label: '基本資料', icon: 'person-outline' },
    { id: 2, label: '詳細設定', icon: 'settings-outline' },
    { id: 3, label: '確認送出', icon: 'checkmark-circle-outline' }
  ];

  // 自定義步驟（無編號）
  customSteps: StepConfig[] = [
    { id: 1, label: '選擇模板', icon: 'layers-outline' },
    { id: 2, label: '配置參數', icon: 'options-outline' },
    { id: 3, label: '預覽結果', icon: 'eye-outline' }
  ];

  // 多步驟流程
  multiSteps: StepConfig[] = [
    { id: 1, label: '需求分析', icon: 'search-outline' },
    { id: 2, label: '方案設計', icon: 'edit-outline' },
    { id: 3, label: '開發實現', icon: 'code-outline' },
    { id: 4, label: '測試驗證', icon: 'checkmark-square-outline' },
    { id: 5, label: '部署上線', icon: 'cloud-upload-outline' }
  ];

  currentStep1 = 2;
  currentStep2 = 1;
  currentStep3 = 3;
  currentStep4 = 3;

  onStepClick(stepNumber: number) {
    console.log('Clickable step clicked:', stepNumber);
    this.currentStep2 = stepNumber;
  }

  onMultiStepClick(stepNumber: number) {
    console.log('Multi-step clicked:', stepNumber);
    this.currentStep4 = stepNumber;
  }

  nextStep() {
    if (this.currentStep1 < this.basicSteps.length) {
      this.currentStep1++;
    }
  }

  prevStep() {
    if (this.currentStep1 > 1) {
      this.currentStep1--;
    }
  }

  reset() {
    this.currentStep1 = 1;
    this.currentStep2 = 1;
    this.currentStep3 = 1;
    this.currentStep4 = 1;
  }
}
