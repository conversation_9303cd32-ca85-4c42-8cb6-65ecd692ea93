import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbIconModule } from '@nebular/theme';
import { StepConfig, StepStatus } from '../../interfaces/step-config.interface';

/**
 * 步驟導航條共用元件
 * 支持自定義步驟配置和狀態管理
 */
@Component({
  selector: 'app-step-navigator',
  standalone: true,
  imports: [
    CommonModule,
    NbIconModule
  ],
  templateUrl: './step-navigator.component.html',
  styleUrls: ['./step-navigator.component.scss']
})
export class StepNavigatorComponent implements OnChanges {
  /** 步驟配置數組 */
  @Input() steps: StepConfig[] = [];
  
  /** 當前步驟索引（1-based） */
  @Input() currentStep: number = 1;
  
  /** 是否允許點擊導航到其他步驟 */
  @Input() allowClickNavigation: boolean = false;
  
  /** 是否顯示步驟編號 */
  @Input() showStepNumber: boolean = true;
  
  /** 自定義CSS類名 */
  @Input() customClass: string = '';
  
  /** 步驟點擊事件 */
  @Output() stepClick = new EventEmitter<number>();
  
  /** 暴露枚舉給模板使用 */
  StepStatus = StepStatus;
  
  /** 處理後的步驟數據 */
  processedSteps: Array<StepConfig & { status: StepStatus; stepNumber: number }> = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['steps'] || changes['currentStep']) {
      this.processSteps();
    }
  }

  /**
   * 處理步驟數據，計算每個步驟的狀態
   */
  private processSteps(): void {
    this.processedSteps = this.steps.map((step, index) => {
      const stepNumber = index + 1;
      let status: StepStatus;
      
      if (stepNumber < this.currentStep) {
        status = StepStatus.COMPLETED;
      } else if (stepNumber === this.currentStep) {
        status = StepStatus.ACTIVE;
      } else {
        status = StepStatus.PENDING;
      }
      
      return {
        ...step,
        status,
        stepNumber
      };
    });
  }

  /**
   * 處理步驟點擊事件
   * @param stepNumber 步驟編號（1-based）
   * @param step 步驟配置
   */
  onStepClick(stepNumber: number, step: StepConfig): void {
    // 檢查是否允許點擊導航
    if (!this.allowClickNavigation) {
      return;
    }
    
    // 檢查步驟是否可點擊
    if (step.clickable === false) {
      return;
    }
    
    // 發出步驟點擊事件
    this.stepClick.emit(stepNumber);
  }

  /**
   * 獲取步驟的CSS類名
   * @param step 處理後的步驟數據
   * @returns CSS類名字符串
   */
  getStepClasses(step: StepConfig & { status: StepStatus; stepNumber: number }): string {
    const classes = ['step-item', step.status];
    
    // 添加可點擊樣式
    if (this.allowClickNavigation && step.clickable !== false) {
      classes.push('clickable');
    }
    
    return classes.join(' ');
  }

  /**
   * 檢查步驟是否可點擊
   * @param step 步驟配置
   * @returns 是否可點擊
   */
  isStepClickable(step: StepConfig): boolean {
    return this.allowClickNavigation && step.clickable !== false;
  }
}
