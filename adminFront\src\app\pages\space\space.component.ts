import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { SharedModule } from '../components/shared.module';
import { CommonModule } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { BaseComponent } from '../components/base/baseComponent';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { SpaceService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';

import { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';
import { tap } from 'rxjs';
import { GetSpaceListResponse, SaveSpaceRequest } from 'src/services/api/models';

export interface selectItem {
  label: string,
  value: number | string,
  key?: string
}

@Component({
  selector: 'ngx-space',
  templateUrl: './space.component.html',
  styleUrls: ['./space.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    BreadcrumbComponent,
  ],
})

export class SpaceComponent extends BaseComponent implements OnInit {
  Math = Math; // 讓模板可以使用 Math 函數

  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;
  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;

  constructor(
    protected override allow: AllowHelper,
    private router: Router,
    private dialogService: NbDialogService,
    private _spaceService: SpaceService,
    private message: MessageService
  ) {
    super(allow)
  }

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;

  spaceList: GetSpaceListResponse[] = [];
  spaceDetail: SaveSpaceRequest = {};
  searchKeyword: string = '';
  searchLocation: string = '';
  searchStatus: number | null = null;

  override ngOnInit(): void {
    this.getSpaceList();
  }

  // 導航到模板管理頁面
  navigateToTemplate(): void {
    this.router.navigate(['/pages/template']);
  }

  getSpaceList(): void {
    const requestData = {
      CPart: this.searchKeyword || null,
      CLocation: this.searchLocation || null,
      CStatus: this.searchStatus,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };

    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: requestData }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.spaceList = response.Entries || [];
          this.totalRecords = response.TotalItems || 0;
        } else {
          this.message.showErrorMSG(response.Message || '載入空間列表失敗');
        }
      })
    ).subscribe();
  }

  onSearch(): void {
    this.pageIndex = 1;
    this.getSpaceList();
  }

  onReset(): void {
    this.searchKeyword = '';
    this.searchLocation = '';
    this.searchStatus = null;
    this.pageIndex = 1;
    this.getSpaceList();
  }

  pageChanged(page: number): void {
    this.pageIndex = page;
    this.getSpaceList();
  }

  openCreateModal(modal: TemplateRef<any>): void {
    this.spaceDetail = { CStatus: 1 };
    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  openEditModal(modal: TemplateRef<any>, item: GetSpaceListResponse): void {
    this.spaceDetail = {
      CSpaceID: item.CSpaceID,
      CPart: item.CPart,
      CLocation: item.CLocation,
      CStatus: item.CStatus
    };
    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  onClose(ref: any): void {
    ref.close();
  }

  onSubmit(ref: any): void {
    if (!this.validateForm()) {
      return;
    }

    const action = this.spaceDetail.CSpaceID ? 'update' : 'create';

    // 直接呼叫 API 保存資料
    this._spaceService.apiSpaceSaveSpacePost$Json({
      body: this.spaceDetail
    }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.message.showSucessMSG(action === 'update' ? '更新空間成功' : '新增空間成功');
          ref.close(); // 關閉對話框
          this.getSpaceList(); // 重新載入列表
        } else {
          this.message.showErrorMSG(response.Message || '儲存空間失敗');
        }
      })
    ).subscribe();
  }

  validateForm(): boolean {
    if (!this.spaceDetail.CPart?.trim()) {
      this.message.showErrorMSG('請輸入項目名稱');
      return false;
    }

    if (!this.spaceDetail.CLocation?.trim()) {
      this.message.showErrorMSG('請輸入所屬區域');
      return false;
    }

    if (this.spaceDetail.CStatus === undefined || this.spaceDetail.CStatus === null) {
      this.message.showErrorMSG('請選擇狀態');
      return false;
    }

    return true;
  }

  deleteSpace(item: GetSpaceListResponse): void {
    if (confirm(`確定要刪除空間「${item.CPart}」嗎？`)) {
      this._spaceService.apiSpaceDeleteSpacePost$Json({
        body: { CSpaceID: item.CSpaceID }
      }).pipe(
        tap(response => {
          if (response.StatusCode === 0) {
            this.message.showSucessMSG('刪除空間成功');
            this.getSpaceList();
          } else {
            this.message.showErrorMSG(response.Message || '刪除空間失敗');
          }
        })
      ).subscribe();
    }
  }
}
