# StepNavigator 步驟導航條共用元件

一個可重用的步驟導航條組件，支持自定義步驟配置、狀態管理和響應式設計。

## 功能特點

- ✅ 自定義步驟配置
- ✅ 三種狀態：待處理(pending)、進行中(active)、已完成(completed)
- ✅ 可選的點擊導航功能
- ✅ 支持圖標顯示
- ✅ 響應式設計
- ✅ 深色主題支持
- ✅ 可自定義樣式

## 基本使用

### 1. 導入組件

```typescript
import { StepNavigatorComponent } from './shared/components/step-navigator/step-navigator.component';
import { StepConfig } from './shared/interfaces/step-config.interface';

@Component({
  // ...
  imports: [
    // ...
    StepNavigatorComponent
  ]
})
```

### 2. 配置步驟

```typescript
export class YourComponent {
  steps: StepConfig[] = [
    { id: 1, label: '選擇模板', icon: 'layers-outline' },
    { id: 2, label: '確認套用', icon: 'checkmark-circle-outline' }
  ];
  
  currentStep = 1;
}
```

### 3. 在模板中使用

```html
<app-step-navigator
  [steps]="steps"
  [currentStep]="currentStep"
  [allowClickNavigation]="false"
  [showStepNumber]="true"
  (stepClick)="onStepClick($event)">
</app-step-navigator>
```

## API 參考

### 輸入屬性 (Input Properties)

| 屬性名 | 類型 | 默認值 | 說明 |
|--------|------|--------|------|
| `steps` | `StepConfig[]` | `[]` | 步驟配置數組 |
| `currentStep` | `number` | `1` | 當前步驟索引（1-based） |
| `allowClickNavigation` | `boolean` | `false` | 是否允許點擊導航 |
| `showStepNumber` | `boolean` | `true` | 是否顯示步驟編號 |
| `customClass` | `string` | `''` | 自定義CSS類名 |

### 輸出事件 (Output Events)

| 事件名 | 參數類型 | 說明 |
|--------|----------|------|
| `stepClick` | `number` | 步驟點擊事件，返回步驟編號 |

### StepConfig 接口

```typescript
interface StepConfig {
  id: string | number;     // 步驟ID
  label: string;           // 步驟顯示文字
  icon?: string;           // 可選圖標（Nebular圖標名稱）
  clickable?: boolean;     // 是否可點擊（默認true）
}
```

## 使用示例

### 基本步驟導航

```typescript
steps: StepConfig[] = [
  { id: 1, label: '基本資料', icon: 'person-outline' },
  { id: 2, label: '詳細設定', icon: 'settings-outline' },
  { id: 3, label: '確認送出', icon: 'checkmark-circle-outline' }
];

currentStep = 2;
```

```html
<app-step-navigator
  [steps]="steps"
  [currentStep]="currentStep">
</app-step-navigator>
```

### 可點擊導航

```typescript
onStepClick(stepNumber: number) {
  // 處理步驟點擊邏輯
  if (this.canNavigateToStep(stepNumber)) {
    this.currentStep = stepNumber;
  }
}
```

```html
<app-step-navigator
  [steps]="steps"
  [currentStep]="currentStep"
  [allowClickNavigation]="true"
  (stepClick)="onStepClick($event)">
</app-step-navigator>
```

### 自定義樣式

```html
<app-step-navigator
  [steps]="steps"
  [currentStep]="currentStep"
  [showStepNumber]="false"
  customClass="my-custom-steps">
</app-step-navigator>
```

```scss
:host ::ng-deep .my-custom-steps {
  .step-nav {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
  }
}
```

## 狀態說明

- **pending（待處理）**: 尚未到達的步驟，顯示為灰色
- **active（進行中）**: 當前步驟，顯示為主色調
- **completed（已完成）**: 已完成的步驟，顯示為綠色並帶有勾選圖標

## 響應式設計

組件內建響應式設計：
- 桌面版：水平排列，顯示連接線
- 平板版：縮小間距和字體
- 手機版：垂直排列，隱藏連接線

## 注意事項

1. 步驟編號是從 1 開始的（1-based）
2. 當 `allowClickNavigation` 為 `false` 時，步驟點擊事件不會觸發
3. 可以通過 `clickable: false` 禁用特定步驟的點擊功能
4. 圖標使用 Nebular 圖標庫，確保已正確導入 `NbIconModule`
