{"ast": null, "code": "/**\n * 步驟狀態枚舉\n */\nexport var StepStatus;\n(function (StepStatus) {\n  /** 待處理 */\n  StepStatus[\"PENDING\"] = \"pending\";\n  /** 進行中 */\n  StepStatus[\"ACTIVE\"] = \"active\";\n  /** 已完成 */\n  StepStatus[\"COMPLETED\"] = \"completed\";\n})(StepStatus || (StepStatus = {}));", "map": {"version": 3, "names": ["StepStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\interfaces\\step-config.interface.ts"], "sourcesContent": ["/**\n * 步驟配置接口\n */\nexport interface StepConfig {\n  /** 步驟ID */\n  id: string | number;\n  /** 步驟顯示文字 */\n  label: string;\n  /** 可選圖標 */\n  icon?: string;\n  /** 是否可點擊 */\n  clickable?: boolean;\n}\n\n/**\n * 步驟狀態枚舉\n */\nexport enum StepStatus {\n  /** 待處理 */\n  PENDING = 'pending',\n  /** 進行中 */\n  ACTIVE = 'active',\n  /** 已完成 */\n  COMPLETED = 'completed'\n}\n"], "mappings": "AAcA;;;AAGA,WAAYA,UAOX;AAPD,WAAYA,UAAU;EACpB;EACAA,UAAA,uBAAmB;EACnB;EACAA,UAAA,qBAAiB;EACjB;EACAA,UAAA,2BAAuB;AACzB,CAAC,EAPWA,UAAU,KAAVA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}